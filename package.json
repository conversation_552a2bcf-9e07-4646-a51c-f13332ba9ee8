{"name": "new.expensify", "version": "9.1.98-2", "author": "Expensify, Inc.", "homepage": "https://new.expensify.com", "description": "New Expensify is the next generation of Expensify: a reimagination of payments based atop a foundation of chat.", "license": "MIT", "private": true, "scripts": {"i-standalone": "STANDALONE_NEW_DOT=true npm i", "install-standalone": "STANDALONE_NEW_DOT=true npm install", "configure-mapbox": "./scripts/setup-mapbox-sdk-walkthrough.sh", "setupNewDotWebForEmulators": "./scripts/setup-newdot-web-emulators.sh", "startAndroidEmulator": "./scripts/start-android.sh", "postinstall": "./scripts/postInstall.sh", "clean": "./scripts/clean.sh", "clean-standalone": "STANDALONE_NEW_DOT=true ./scripts/clean.sh", "android": "./scripts/set-pusher-suffix.sh && ./scripts/run-build.sh --android", "android-standalone": "./scripts/set-pusher-suffix.sh && STANDALONE_NEW_DOT=true ./scripts/run-build.sh --android", "ios": "./scripts/set-pusher-suffix.sh && ./scripts/run-build.sh --ios", "ios-standalone": "./scripts/set-pusher-suffix.sh && STANDALONE_NEW_DOT=true ./scripts/run-build.sh --ios", "pod-install": "./scripts/pod-install.sh", "pod-install-standalone": "STANDALONE_NEW_DOT=true ./scripts/pod-install.sh", "ipad": "concurrently \"./scripts/run-build.sh --ipad\"", "ipad-standalone": "concurrently \"STANDALONE_NEW_DOT=true ./scripts/run-build.sh --ipad\"", "ipad-sm": "concurrently \"./scripts/run-build.sh --ipad-sm\"", "ipad-sm-standalone": "concurrently \"STANDALONE_NEW_DOT=true ./scripts/run-build.sh --ipad-sm\"", "start": "npx rnef start", "web": "./scripts/set-pusher-suffix.sh && concurrently npm:web-proxy npm:web-server", "web-proxy": "ts-node web/proxy.ts", "web-server": "./scripts/start-dev-with-auto-restart.sh", "build": "webpack --config config/webpack/webpack.common.ts --env file=.env.production && ts-node ./scripts/combine-web-sourcemaps.ts", "build-staging": "webpack --config config/webpack/webpack.common.ts --env file=.env.staging && ts-node ./scripts/combine-web-sourcemaps.ts", "build-adhoc": "webpack --config config/webpack/webpack.common.ts --env file=.env.adhoc && ts-node ./scripts/combine-web-sourcemaps.ts", "desktop": "./scripts/set-pusher-suffix.sh && ts-node desktop/start.ts", "desktop-build": "./scripts/build-desktop.sh production", "desktop-build-staging": "./scripts/build-desktop.sh staging", "createDocsRoutes": "ts-node .github/scripts/createDocsRoutes.ts", "detectRedirectCycle": "ts-node .github/scripts/detectRedirectCycle.ts", "desktop-build-adhoc": "./scripts/build-desktop.sh adhoc", "ios-build": "bundle exec fastlane ios build_unsigned", "ios-hybrid-build": "bundle exec fastlane ios build_unsigned_hybrid", "android-build": "bundle exec fastlane android build_local", "android-hybrid-build": "bundle exec fastlane android build_local_hybrid", "test": "TZ=utc NODE_OPTIONS=--experimental-vm-modules jest", "test:debug": "TZ=utc NODE_OPTIONS='--inspect-brk --experimental-vm-modules' jest --runInBand", "perf-test": "NODE_OPTIONS=--experimental-vm-modules npx reassure", "typecheck": "NODE_OPTIONS=--max_old_space_size=8192 tsc", "lint": "NODE_OPTIONS=--max_old_space_size=8192 eslint . --max-warnings=243 --cache --cache-location=node_modules/.cache/eslint", "lint-changed": "NODE_OPTIONS=--max_old_space_size=8192 ./scripts/lintChanged.sh", "lint-watch": "npx eslint-watch --watch --changed", "shellcheck": "./scripts/shellCheck.sh", "prettier": "prettier --write .", "prettier-changed": "prettier --write --ignore-unknown $(git diff --diff-filter=AMR --name-only origin/main HEAD)", "prettier-watch": "onchange \"**/*.{js,mjs,ts,tsx}\" -- prettier --write --ignore-unknown {{changed}}", "print-version": "echo $npm_package_version", "storybook": "storybook dev -p 6006", "storybook-build": "ENV=production storybook build -o dist/docs", "storybook-build-staging": "ENV=staging storybook build -o dist/docs", "gh-actions-build": "./.github/scripts/buildActions.sh", "gh-actions-validate": "./.github/scripts/validateActionsAndWorkflows.sh", "analyze-packages": "ANALYZE_BUNDLE=true webpack --config config/webpack/webpack.common.ts --env file=.env.production", "symbolicate:android": "npx metro-symbolicate android/app/build/generated/sourcemaps/react/release/index.android.bundle.map", "symbolicate:ios": "npx metro-symbolicate main.jsbundle.map", "symbolicate-release:ios": "./scripts/release-profile.ts --platform=ios", "symbolicate-release:android": "./scripts/release-profile.ts --platform=android", "symbolicate-release:web": "./scripts/release-profile.ts --platform=web", "symbolicate-profile": "./scripts/symbolicate-profile.ts", "combine-web-sourcemaps": "./scripts/combine-web-sourcemaps.ts", "test:e2e": "ts-node tests/e2e/testRunner.ts --config ./config.local.ts", "test:e2e:dev": "ts-node tests/e2e/testRunner.ts --config ./config.dev.ts", "gh-actions-unused-styles": "npx ts-node scripts/findUnusedStyles.ts", "setup-https": "mkcert -install && mkcert -cert-file config/webpack/certificate.pem -key-file config/webpack/key.pem dev.new.expensify.com localhost 127.0.0.1", "e2e-test-runner-build": "node --max-old-space-size=8192 node_modules/.bin/ncc build tests/e2e/testRunner.ts -o tests/e2e/dist/", "react-compiler-healthcheck": "react-compiler-healthcheck --verbose", "react-compiler-healthcheck-test": "react-compiler-healthcheck --verbose &> react-compiler-output.txt", "generate-search-parser": "peggy --format es -o src/libs/SearchParser/searchParser.js src/libs/SearchParser/searchParser.peggy src/libs/SearchParser/baseRules.peggy", "generate-autocomplete-parser": "peggy --format es -o src/libs/SearchParser/autocompleteParser.js src/libs/SearchParser/autocompleteParser.peggy src/libs/SearchParser/baseRules.peggy && ./scripts/parser-workletization.sh src/libs/SearchParser/autocompleteParser.js", "web:prod": "http-server ./dist --cors", "octokit": "cd scripts && npx ts-node -i -e \"$(cat ./octokit.ts)\""}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dotlottie/react-player": "^1.6.3", "@expensify/nitro-utils": "file:./modules/ExpensifyNitroUtils", "@expensify/react-native-background-task": "file:./modules/background-task", "@expensify/react-native-hybrid-app": "file:./modules/hybrid-app", "@expensify/react-native-live-markdown": "0.1.301", "@expensify/react-native-wallet": "^0.1.5", "@expo/metro-runtime": "^5.0.4", "@firebase/app": "^0.13.2", "@firebase/performance": "^0.6.8", "@formatjs/intl-listformat": "^7.5.7", "@formatjs/intl-locale": "^4.0.0", "@formatjs/intl-numberformat": "^8.10.3", "@formatjs/intl-pluralrules": "^5.2.14", "@fullstory/browser": "^2.0.6", "@fullstory/react-native": "^1.7.6", "@gorhom/portal": "^1.0.14", "@invertase/react-native-apple-authentication": "^2.2.2", "@onfido/react-native-sdk": "10.6.0", "@pusher/pusher-websocket-react-native": "^1.3.1", "@react-native-camera-roll/camera-roll": "7.4.0", "@react-native-clipboard/clipboard": "^1.15.0", "@react-native-community/geolocation": "3.3.0", "@react-native-community/netinfo": "11.2.1", "@react-native-documents/picker": "^10.1.3", "@react-native-firebase/analytics": "^22.2.1", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/crashlytics": "^22.2.1", "@react-native-firebase/perf": "^22.2.1", "@react-native-google-signin/google-signin": "^10.0.1", "@react-native-picker/picker": "2.9.0", "@react-navigation/material-top-tabs": "7.2.13", "@react-navigation/native": "7.1.10", "@react-navigation/native-stack": "7.3.14", "@react-navigation/stack": "7.3.3", "@react-ng/bounds-observer": "^0.2.1", "@rnmapbox/maps": "10.1.33", "@shopify/flash-list": "1.8.2", "@ua/react-native-airship": "~24.4.0", "awesome-phonenumber": "^5.4.0", "babel-polyfill": "^6.26.0", "canvas-size": "^1.2.6", "core-js": "^3.32.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dom-serializer": "^0.2.2", "domhandler": "^4.3.0", "expensify-common": "2.0.156", "expo": "53.0.7", "expo-asset": "^11.1.2", "expo-av": "^15.1.5", "expo-font": "^13.0.4", "expo-image": "^2.1.6", "expo-image-manipulator": "^13.1.5", "fast-equals": "^5.2.2", "focus-trap-react": "^11.0.3", "heic-to": "^1.1.13", "howler": "^2.2.4", "htmlparser2": "^7.2.0", "idb-keyval": "^6.2.1", "jszip": "^3.10.1", "lodash-es": "4.17.21", "lottie-react-native": "6.5.1", "mapbox-gl": "^2.15.0", "onfido-sdk-ui": "14.42.0", "pako": "^2.1.0", "process": "^0.11.10", "pusher-js": "8.3.0", "react": "19.0.0", "react-collapse": "^5.1.0", "react-content-loader": "^7.0.0", "react-dom": "19.0.0", "react-error-boundary": "^4.0.11", "react-fast-pdf": "^1.0.27", "react-map-gl": "^7.1.3", "react-native": "0.79.2", "react-native-advanced-input-mask": "1.3.1", "react-native-android-location-enabler": "^2.0.1", "react-native-app-logs": "0.3.1", "react-native-blob-util": "0.19.4", "react-native-collapsible": "^1.6.2", "react-native-config": "1.5.3", "react-native-device-info": "10.3.1", "react-native-draggable-flatlist": "^4.0.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "2.25.0", "react-native-google-places-autocomplete": "2.5.6", "react-native-haptic-feedback": "^2.3.3", "react-native-image-picker": "^7.1.2", "react-native-image-size": "git+https://github.com/Expensify/react-native-image-size#cb392140db4953a283590d7cf93b4d0461baa2a9", "react-native-key-command": "1.0.14", "react-native-keyboard-controller": "1.18.1", "react-native-launch-arguments": "^4.0.2", "react-native-localize": "^2.2.6", "react-native-modal": "^13.0.0", "react-native-nitro-modules": "0.26.2", "react-native-nitro-sqlite": "9.1.10", "react-native-onyx": "2.0.132", "react-native-pager-view": "6.5.3", "react-native-pdf": "6.7.3", "react-native-performance": "^5.1.4", "react-native-permissions": "^5.4.0", "react-native-picker-select": "git+https://github.com/Expensify/react-native-picker-select.git#07d60d78d4772d47afd7a744940fc6b6d1881806", "react-native-plaid-link-sdk": "11.11.0", "react-native-qrcode-svg": "6.3.14", "react-native-reanimated": "3.19.1", "react-native-release-profiler": "^0.2.1", "react-native-render-html": "6.3.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "4.12.0", "react-native-share": "11.0.2", "react-native-sound": "^0.11.2", "react-native-svg": "15.9.0", "react-native-tab-view": "^4.1.0", "react-native-url-polyfill": "^2.0.0", "react-native-view-shot": "4.0.0", "react-native-vision-camera": "^4.7.0", "react-native-web": "0.20.0", "react-native-webview": "13.13.1", "react-plaid-link": "3.3.2", "react-web-config": "^1.0.0", "react-webcam": "^7.1.1"}, "devDependencies": {"@actions/core": "1.10.0", "@actions/github": "5.1.1", "@babel/core": "^7.25.2", "@babel/parser": "^7.22.16", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-class-properties": "^7.25.4", "@babel/preset-env": "^7.25.3", "@babel/preset-flow": "^7.12.13", "@babel/preset-react": "^7.10.4", "@babel/preset-typescript": "^7.21.5", "@babel/runtime": "^7.25.0", "@babel/traverse": "^7.22.20", "@babel/types": "^7.22.19", "@callstack/reassure-compare": "^1.0.0-rc.4", "@dword-design/eslint-plugin-import-alias": "^5.0.0", "@electron/notarize": "^2.5.0", "@fullstory/babel-plugin-annotate-react": "^2.3.0", "@fullstory/babel-plugin-react-native": "^1.2.1", "@jest/globals": "^29.7.0", "@ngneat/falso": "^7.1.1", "@octokit/core": "4.0.4", "@octokit/plugin-paginate-rest": "3.1.0", "@octokit/plugin-throttling": "4.1.0", "@octokit/webhooks-types": "^7.5.1", "@perf-profiler/profiler": "^0.10.10", "@perf-profiler/reporter": "^0.9.0", "@perf-profiler/types": "^0.8.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native-community/eslint-config": "3.2.0", "@react-native/babel-preset": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-navigation/devtools": "^6.0.10", "@rnef/cli": "0.8.7", "@rnef/platform-android": "0.8.7", "@rnef/platform-ios": "0.8.7", "@rnef/plugin-metro": "0.8.7", "@rnef/provider-github": "0.8.7", "@storybook/addon-a11y": "^8.6.9", "@storybook/addon-essentials": "^8.6.9", "@storybook/addon-webpack5-compiler-babel": "^3.0.5", "@storybook/cli": "^8.6.14", "@storybook/manager-api": "^8.6.9", "@storybook/react": "^8.6.9", "@storybook/react-webpack5": "^8.6.9", "@storybook/theming": "^8.6.9", "@svgr/webpack": "^6.0.0", "@testing-library/react-native": "13.2.0", "@trivago/prettier-plugin-sort-imports": "^4.2.0", "@types/base-64": "^1.0.2", "@types/canvas-size": "^1.2.2", "@types/concurrently": "^7.0.0", "@types/howler": "^2.2.12", "@types/jest": "^29.5.14", "@types/jest-when": "^3.5.2", "@types/js-yaml": "^4.0.5", "@types/lodash-es": "4.17.12", "@types/mapbox-gl": "^2.7.13", "@types/mime-db": "^1.43.5", "@types/node": "^20.11.5", "@types/pako": "^2.0.3", "@types/pusher-js": "^5.1.0", "@types/react": "^19.0.0", "@types/react-collapse": "^5.0.1", "@types/react-dom": "^19.0.0", "@types/react-is": "^18.3.0", "@types/react-native-web": "^0.0.0", "@types/react-test-renderer": "^19.0.0", "@types/semver": "^7.5.4", "@types/setimmediate": "^1.0.2", "@types/webpack": "^5.28.5", "@types/webpack-bundle-analyzer": "^4.7.0", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "@vercel/ncc": "0.38.1", "@vue/preload-webpack-plugin": "^2.0.0", "@welldone-software/why-did-you-render": "7.0.1", "ajv-cli": "^5.0.0", "babel-jest": "29.7.0", "babel-loader": "^9.1.3", "babel-plugin-module-resolver": "^5.0.0", "babel-plugin-react-compiler": "^19.0.0-beta-8a03594-20241020", "babel-plugin-react-native-web": "^0.18.7", "babel-plugin-transform-remove-console": "^6.9.4", "clean-webpack-plugin": "^4.0.0", "concurrently": "^8.2.2", "copy-webpack-plugin": "^10.1.0", "css-loader": "^6.7.2", "csv-parse": "^5.5.5", "csv-writer": "^1.6.0", "diff-so-fancy": "^1.3.0", "dotenv": "^16.0.3", "electron": "^37.2.4", "electron-builder": "25.0.0", "eslint": "^8.57.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-expensify": "2.0.86", "eslint-config-prettier": "^9.1.0", "eslint-plugin-deprecation": "^3.0.0", "eslint-plugin-jest": "^28.6.0", "eslint-plugin-jsdoc": "^46.2.6", "eslint-plugin-lodash": "^7.4.0", "eslint-plugin-react-compiler": "^19.0.0-beta-8a03594-20241020", "eslint-plugin-react-native-a11y": "^3.3.0", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-testing-library": "^6.2.2", "eslint-plugin-you-dont-need-lodash-underscore": "^6.14.0", "glob": "^10.3.0", "googleapis": "^144.0.0", "html-webpack-plugin": "^5.5.0", "http-server": "^14.1.1", "jest": "29.7.0", "jest-circus": "29.7.0", "jest-cli": "29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-expo": "53.0.4", "jest-transformer-svg": "^2.0.1", "jest-when": "^3.5.2", "link": "^2.1.1", "memfs": "^4.6.0", "nitro-codegen": "0.25.2", "onchange": "^7.1.0", "openai": "5.5.1", "patch-package": "^8.1.0-canary.1", "peggy": "^4.0.3", "portfinder": "^1.0.28", "prettier": "3.5.3", "react-compiler-healthcheck": "^19.0.0-beta-8a03594-20241020", "react-compiler-runtime": "^19.0.0-beta-8a03594-20241020", "react-is": "^18.3.1", "react-native-clean-project": "^4.0.0-alpha4.0", "react-refresh": "^0.14.2", "react-test-renderer": "19.0.0", "reassure": "^1.0.0-rc.4", "semver": "7.5.2", "setimmediate": "^1.0.5", "shellcheck": "^1.1.0", "source-map": "^0.7.4", "storybook": "^8.6.9", "style-loader": "^2.0.0", "time-analytics-webpack-plugin": "^0.1.17", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "type-fest": "4.35.0", "typescript": "^5.9.2", "wait-port": "^0.2.9", "webpack": "^5.94.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^5.0.4", "webpack-dev-server": "^5.2.1", "webpack-merge": "^5.8.0", "xlsx": "file:vendor/xlsx-0.20.3.tgz"}, "overrides": {"braces": "3.0.3", "yargs": "17.7.2", "yargs-parser": "21.1.1", "ws": "8.17.1", "micromatch": "4.0.8", "json5": "2.2.2", "loader-utils": "2.0.4", "follow-redirects": "1.15.6", "fast-xml-parser": "4.4.1", "express": "4.20.0", "elliptic": "6.5.7", "fast-json-patch": "3.1.1", "webpack": "^5.94.0", "@blakeembrey/template": "1.2.0", "body-parser": "1.20.3", "path-to-regexp": "0.1.10", "send": "0.19.0", "regexpu-core": "5.3.2", "react": "19.0.0", "expo": "53.0.7", "react-dom": "19.0.0"}, "expo": {"autolinking": {"exclude": ["expo-constants", "expo-file-system", "@react-native-google-signin/google-signin", "expo-keep-awake"]}}, "electronmon": {"patterns": ["!src/**", "!ios/**", "!android/**", "!tests/**", "*.test.*"]}, "engines": {"node": "20.19.3", "npm": "10.8.2"}}