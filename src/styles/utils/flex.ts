import type {ViewStyle} from 'react-native';

/**
 * Flex layout utility styles with Bootstrap inspired naming.
 *
 * https://getbootstrap.com/docs/5.0/utilities/flex/
 */
export default {
    flexReset: {
        flex: undefined,
    },

    flex0: {
        flex: 0,
    },

    flex1: {
        flex: 1,
    },

    flex2: {
        flex: 2,
    },

    flex3: {
        flex: 3,
    },

    flex4: {
        flex: 4,
    },

    flex5: {
        flex: 5,
    },

    flexRow: {
        flexDirection: 'row',
    },

    flexColumn: {
        flexDirection: 'column',
    },

    flexRowReverse: {
        flexDirection: 'row-reverse',
    },

    flexColumnReverse: {
        flexDirection: 'column-reverse',
    },
    justifyContentCenter: {
        justifyContent: 'center',
    },

    justifyContentStart: {
        justifyContent: 'flex-start',
    },

    justifyContentEnd: {
        justifyContent: 'flex-end',
    },

    justifyContentBetween: {
        justifyContent: 'space-between',
    },

    justifyContentAround: {
        justifyContent: 'space-around',
    },

    justifyContentEvenly: {
        justifyContent: 'space-evenly',
    },

    alignSelfStretch: {
        alignSelf: 'stretch',
    },

    alignSelfCenter: {
        alignSelf: 'center',
    },

    alignSelfStart: {
        alignSelf: 'flex-start',
    },

    alignSelfEnd: {
        alignSelf: 'flex-end',
    },

    alignItemsStart: {
        alignItems: 'flex-start',
    },

    alignItemsCenter: {
        alignItems: 'center',
    },

    alignItemsEnd: {
        alignItems: 'flex-end',
    },

    alignItemsBaseline: {
        alignItems: 'baseline',
    },

    alignItemsStretch: {
        alignItems: 'stretch',
    },

    flexWrap: {
        flexWrap: 'wrap',
    },

    flexNoWrap: {
        flexWrap: 'nowrap',
    },

    flexGrow0: {
        flexGrow: 0,
    },

    flexGrow1: {
        flexGrow: 1,
    },

    flexGrow2: {
        flexGrow: 2,
    },

    flexGrow4: {
        flexGrow: 4,
    },

    flexShrink2: {
        flexShrink: 2,
    },

    flexShrink1: {
        flexShrink: 1,
    },

    flexShrink0: {
        flexShrink: 0,
    },

    flexBasisAuto: {
        flexBasis: 'auto',
    },

    flexBasis100: {
        flexBasis: '100%',
    },

    flexBasis0: {
        flexBasis: 0,
    },
} satisfies Record<string, ViewStyle>;
