# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
*.cer
*.p12
*.mobileprovision
ios-fastlane-json-key.json
**/.xcode.env.local
ios/tmp.xcconfig

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof
android/app/src/main/java/com/expensify/chat/generated/
.cxx/

# VIM
*.swp
*.swo
*~

# Vscode
.vscode

# Fleet
.fleet

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# Bundled code
dist/
desktop-build/

# BUCK
buck-out/
\.buckd/
*.keystore
!debug.keystore
android/app/android-fastlane-json-key.json

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Bundle artifact
*.jsbundle

# Ruby / CocoaPods
**/Pods/
/vendor/bundle/

# Local DEV config
/.env

# Storybook build files
storybook-static

# GitHub GPG Keys
.github/workflows/OSBotify-private-key.asc
*.asc

# Jest coverage report
/coverage.data
/coverage/

.jest-cache

# E2E test reports
tests/e2e/results/
.reassure

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# Typescript
tsconfig.tsbuildinfo

# Mock-github
/repo/

# Yalc
.yalc
yalc.lock

# Expo
.expo
dist/
web-build/

# Local https certificate/key
config/webpack/*.pem

# Expo
.expo
dist/
web-build/

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Storage location for downloaded app source maps (see scripts/symbolicate-profile.ts)
.sourcemaps/

# Jeykll
docs/.bundle

# Output of react compiler healthcheck dev script
react-compiler-output.txt

# React Native Enterprise Framework
.rnef/

# Generated by bob (for Nitro modules)
modules/*/lib/

# Claude local settings
.claude/settings.local.json
