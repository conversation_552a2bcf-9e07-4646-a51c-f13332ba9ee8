---
layout: product
title: Expensify Chat
---

# Chat

Chat is the foundation of New Expensify. Every expense, expense report, workspace, or member has an associated "chat", which you can use to record additional details, or collaborate with others. Every chat has the following components:

## Header

This shows who you are chatting with (or what you are chatting about). You can press the header for more details on the chat, or additional actions to take upon it.

## Comments

The core of the chat are its comments, which come in many forms:

- **Text** – Rich text messages stored securely and delivered via web, app, email, or SMS.  
- **Images & Documents** – Insert photos, screenshots, movies, PDFs, or more, using copy/paste, drag/drop, or the attach button.
- **Expenses** – Share an expense in the chat, either to simply track and document it, or to submit for reimbursement.
- **Tasks** – Record a task, and optionally assign it to someone (or yourself!).

## Actions

Hover (or long press) on a comment to see additional options, including:

- **React** – Throw a ♥️😂🔥 like on anything!
- **Reply in thread** – Go deeper by creating a new chat on any comment.
- **Mark unread** – Flag it for reading later, at your convenience.

## Composer

Use the composer at the bottom to write new messages:

- **Markdown** – Format text using **bold**, *italics*, and [more](https://help.expensify.com/articles/new-expensify/chat/Send-and-format-chat-messages).  
- **Mention** – Invite or tag anyone in the world to any chat by putting an `@` in front of their email address or phone number (e.g., **@<EMAIL>**, or **@**************).
