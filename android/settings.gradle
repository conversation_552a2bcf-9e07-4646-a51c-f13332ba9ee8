pluginManagement { includeBuild("../node_modules/@react-native/gradle-plugin") }
plugins { id("com.facebook.react.settings") }
apply from: "${rootDir}/../gradleUtils/PatchedArtifactsSettings.gradle"

extensions.configure(com.facebook.react.ReactSettingsExtension){ ex -> ex.autolinkLibrariesFromCommand(['npx', 'rnef', 'config', '-p', 'android']) }
rootProject.name = 'NewExpensify'
include ':react-native-webview'
project(':react-native-webview').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-webview/android')
include ':react-native-image-size'
project(':react-native-image-size').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-image-size/android')
include ':react-native-config'
project(':react-native-config').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-config/android')
include ':react-native-plaid-link-sdk'
project(':react-native-plaid-link-sdk').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-plaid-link-sdk/android')
include ':app'
includeBuild('../node_modules/@react-native/gradle-plugin')

if(settings.extensions.patchedArtifacts.buildFromSource) {
    includeBuild('../node_modules/react-native') {
        dependencySubstitution {
            substitute(module("com.facebook.react:react-android")).using(project(":packages:react-native:ReactAndroid"))
            substitute(module("com.facebook.react:react-native")).using(project(":packages:react-native:ReactAndroid"))
            substitute(module("com.facebook.react:hermes-android")).using(project(":packages:react-native:ReactAndroid:hermes-engine"))
            substitute(module("com.facebook.react:hermes-engine")).using(project(":packages:react-native:ReactAndroid:hermes-engine"))
        }
    }
}

apply from: new File(["node", "--print", "require.resolve('expo/package.json')"].execute(null, rootDir).text.trim(), "../scripts/autolinking.gradle")
useExpoModules()
