diff --git a/node_modules/react-native-web/dist/vendor/react-native/VirtualizedList/index.js b/node_modules/react-native-web/dist/vendor/react-native/VirtualizedList/index.js
index 165bf95..e80c5b1 100644
--- a/node_modules/react-native-web/dist/vendor/react-native/VirtualizedList/index.js
+++ b/node_modules/react-native-web/dist/vendor/react-native/VirtualizedList/index.js
@@ -719,6 +719,16 @@ class VirtualizedList extends StateSafePureComponent {
         if (this.props.inverted && this._scrollRef && this._scrollRef.getScrollableNode) {
             const node = this._scrollRef.getScrollableNode();
             
+            const scrollMetricsOffset = this._scrollMetrics.offset;
+            const contentLength = this._scrollMetrics.contentLength;
+            const visibleLength = this._scrollMetrics.visibleLength
+            const isOnScrollLimit = scrollMetricsOffset <= 0 || Math.ceil(scrollMetricsOffset) >= contentLength - visibleLength;
+            
+            // Avoid recoil/rubber band effect on overscroll
+            if (isOnScrollLimit) {
+              ev.preventDefault();
+            }
+
             if (isHorizontal) {
                 if (Math.abs(deltaX) > Math.abs(deltaY)) {
                   ev.target.scrollLeft += targetDelta;
