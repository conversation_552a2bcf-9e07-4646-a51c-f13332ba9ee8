name: Jest Unit Tests

on:
  workflow_call:
  pull_request:
    types: [opened, synchronize]
    branches-ignore: [staging, production]
    paths: ['**.js', '**.ts', '**.tsx', 'package.json', 'package-lock.json']

concurrency:
  group: ${{ github.ref == 'refs/heads/main' && format('{0}-{1}', github.ref, github.sha) || github.ref }}-jest
  cancel-in-progress: true

jobs:
  jest:
    if: ${{ github.event.head_commit.author.name != 'OSBotify' && github.event.head_commit.author.name != 'imgbot[bot]' || github.event_name == 'workflow_call' }}
    runs-on: ubuntu-latest
    env:
      CI: true
    strategy:
      fail-fast: false
      matrix:
        chunk: [ 1, 2, 3 ]
    name: test (job ${{ fromJSON(matrix.chunk) }})
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608

      - name: Setup Node
        uses: ./.github/actions/composite/setupNode

      - name: Get number of CPU cores
        id: cpu-cores
        uses: SimenB/github-actions-cpu-cores@31e91de0f8654375a21e8e83078be625380e2b18

      - name: Cache Jest cache
        id: cache-jest-cache
        # v4
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57
        with:
          path: .jest-cache
          key: ${{ runner.os }}-jest

      - name: Jest tests
        run: NODE_OPTIONS="$NODE_OPTIONS --experimental-vm-modules" npm test -- --silent --shard=${{ fromJSON(matrix.chunk) }}/${{ strategy.job-total }} --max-workers ${{ steps.cpu-cores.outputs.count }} --coverage --coverageDirectory=coverage/shard-${{ matrix.chunk }}

      - name: Upload coverage to Codecov (PRs - tokenless)
        if: ${{ github.event_name == 'pull_request' }}
        # v5
        uses: codecov/codecov-action@5a1091511ad55cbe89839c7260b706298ca349f7
        with:
          files: coverage/shard-${{ matrix.chunk }}/coverage-final.json,coverage/shard-${{ matrix.chunk }}/lcov.info
          flags: unit
          name: jest-${{ matrix.chunk }}
          disable_search: true
          fail_ci_if_error: true

      - name: Load Codecov token from 1Password
        id: load-codecov-token
        # Codecov needs a token to be run on the default branch (which happens in preDeploy.yml after a PR is merged to main)
        if: ${{ github.event_name == 'push' && github.ref == 'refs/heads/main' }}
        # v2
        uses: 1password/load-secrets-action@581a835fb51b8e7ec56b71cf2ffddd7e68bb25e0
        with:
          export-env: false
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}
          CODECOV_TOKEN: op://${{ vars.OP_VAULT }}//CodeCov-API/key

      - name: Upload coverage to Codecov (token required)
        if: ${{ github.event_name == 'push' && github.ref == 'refs/heads/main' }}
        # v5
        uses: codecov/codecov-action@5a1091511ad55cbe89839c7260b706298ca349f7
        with:
          token: ${{ steps.load-codecov-token.outputs.CODECOV_TOKEN }}
          files: coverage/shard-${{ matrix.chunk }}/coverage-final.json,coverage/shard-${{ matrix.chunk }}/lcov.info
          flags: unit,shard-${{ matrix.chunk }}
          name: jest-${{ matrix.chunk }}
          disable_search: true
          fail_ci_if_error: true

  storybookTests:
    if: ${{ github.event.head_commit.author.name != 'OSBotify' && github.event.head_commit.author.name != 'imgbot[bot]' || github.event_name == 'workflow_call' }}
    runs-on: ubuntu-latest
    name: Storybook tests
    steps:
      - uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608 # v4

      - uses: ./.github/actions/composite/setupNode

      - name: Storybook run
        run: npm run storybook -- --smoke-test --ci
