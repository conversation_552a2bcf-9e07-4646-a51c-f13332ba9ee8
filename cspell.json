{"language": "en", "words": ["--longpress", "<PERSON><PERSON><PERSON>", "achreimburse", "adbd", "ADDCOMMENT", "Addendums", "ADFS", "<PERSON><PERSON><PERSON>", "Aeroplan", "<PERSON><PERSON><PERSON>", "Aircall", "airshipconfig", "airside", "<PERSON><PERSON>", "<PERSON><PERSON>'s", "americanexpressfdx", "<PERSON><PERSON>", "androiddebugkey", "androidx", "APCA", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "apktool", "APPL", "applauseleads", "<PERSON><PERSON><PERSON>", "appleid", "applesignin", "applinks", "approvable", "approvalstatus", "appversion", "archivado", "ARGB", "<PERSON><PERSON><PERSON>", "armv7", "ARNK", "ARROWDOWN", "ARROWLEFT", "ARROWRIGHT", "ARROWUP", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "as_siteseach", "asar", "ASPAC", "assetlinks", "attributes.accountid", "attributes.reportid", "authorised", "<PERSON><PERSON>", "AUTOAPPROVE", "autocompletions", "autodocs", "autofilled", "automations", "autoplay", "AUTOREIMBURSED", "autoreleasepool", "AUTOREPORTING", "autoresizesSubviews", "autoresizing", "autosync", "avds", "AVURL", "<PERSON><PERSON>", "basehead", "<PERSON><PERSON><PERSON>", "behaviour", "bigdecimal", "BILLCOM", "billpay", "Bill<PERSON>y", "blahbla<PERSON>blah", "b<PERSON><PERSON><PERSON><PERSON>", "blankrows", "BNDL", "<PERSON><PERSON>", "bofa", "bolditalic", "bootsplash", "Borderless", "Botify", "brex", "bridgeless", "Broadwoven", "Bronn", "BROWSABLE", "buildscript", "Buildscript", "Bushwick", "capitalone", "CAROOT", "Carta", "ccache", "ccupload", "cdfbmo", "Certinia", "Certinia's", "CFPB", "chargeback", "<PERSON><PERSON>", "Checkmark", "checkmarked", "chien", "Chronos", "citi", "clawback", "cleartext", "CLIA", "CLIENTID", "clippath", "Cliqbook", "cloudflarestream", "cmaps", "cmjs", "cocoapods", "Codat", "codegen", "codeshare", "Codice", "Combustors", "contenteditable", "copiloted", "copiloting", "Corpay", "Countertop", "CPPFLAGS", "cpuprofile", "creditamount", "CREDS", "crios", "csvg", "customairshipextender", "customfield", "customise", "dateexported", "debitamount", "deburr", "deburred", "<PERSON><PERSON>", "deeplink", "deeplinked", "deeplinking", "deeplinks", "delegators", "delish", "deployers", "<PERSON><PERSON>", "De<PERSON>ch", "devportal", "DFOLLY", "diems", "dimen", "directfeeds", "<PERSON><PERSON><PERSON>", "DocuSign", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dont", "<PERSON><PERSON><PERSON>", "Drycleaners", "Drycleaning", "DSYM", "dsyms", "<PERSON><PERSON><PERSON><PERSON>", "e2edelta", "ecash", "ecconnrefused", "econn", "EDIFACT", "Egencia", "Electromedical", "electronmon", "Electrotherapeutic", "ellipsize", "EMEA", "Emphemeral", "endcapture", "enddate", "endfor", "enroute", "entityid", "Entra", "ENVFILE", "Ephermeral", "eraa", "ERECEIPT", "ERECEIPTS", "Español", "ESTA", "ethnicities", "eticket", "EUVAT", "evenodd", "eventmachine", "evictable", "exchangerate", "exchrate", "exfy", "exitstatus", "Expatriot", "Expensable", "expensescount", "Expensi", "Expensicon", "Expensicons", "expensicorp", "Expensifier", "EXPENSIFYAPI", "expensifyhelp", "expensifylite", "expensifymono", "expensifyneue", "expensifynewkansas", "EXPENSIFYPDBUSINESS", "EXPENSIFYPDTEAM", "expensifyreactnative", "EXPENSIFYWEB", "Expensiworks", "<PERSON><PERSON><PERSON>", "fabs", "falso", "favicons", "Ferroalloy", "firstname", "<PERSON><PERSON><PERSON>", "flac", "flatlist", "flexsearch", "FLJZ", "fname", "fnames", "focusvisible", "fontawesome", "foreignamount", "formatjs", "Français", "<PERSON><PERSON>", "frontpart", "fullstory", "FWTV", "FXHF", "<PERSON><PERSON>", "gastos", "GBRRBR", "gcsc", "gcse", "GDSO", "genkey", "GEOLOCATION", "getprop", "glcode", "<PERSON><PERSON><PERSON><PERSON>", "googleusercontent", "gorhom", "gpgsign", "gradlew", "Grantmaking", "gscb", "gsib", "gsst", "Gsuite", "Handtool", "hanno", "hanno_<PERSON><PERSON><PERSON><PERSON>", "headshot", "healthcheck", "Heathrow", "helpsite", "Highfive", "Highlightable", "Hoverable", "HRMS", "HSBCSGS", "hybrid<PERSON>p", "Hydronics", "iaco", "IBTA", "IDEIN", "idfa", "Idology", "ifdef", "imagebutton", "Inactives", "inbetweenCompo", "Inclusivity", "initialises", "inputmethod", "instancetype", "Intacct", "intenthandler", "ionatan", "iOSQRCode", "IPHONEOS", "iphonesimulator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "islarge", "ismedium", "isnonreimbursable", "issmall", "Italiano", "ITSM", "<PERSON><PERSON><PERSON>", "jank", "janky", "jarsigner", "johndoe", "jsbundle", "jsSrcsDir", "<PERSON><PERSON><PERSON>", "keyalg", "keycommand", "keyevent", "keypass", "keysize", "keytool", "keyval", "keyvaluepairs", "KHTML", "killall", "<PERSON><PERSON><PERSON>", "Krasoń", "<PERSON>by", "<PERSON><PERSON><PERSON>", "laggy", "lastiPhoneLogin", "lastname", "LDFLAGS", "LHNGBR", "LIBCPP", "libexec", "licence", "LIGHTBOXES", "Limpich", "linecap", "linejoin", "listformat", "LLDB", "<PERSON><PERSON><PERSON>", "logcat", "logomark", "Lothbrok", "lucene", "<PERSON><PERSON>", "Maat", "<PERSON><PERSON>", "maildrop", "manualreimburse", "Mapbox", "mapboxgl", "marcaaron", "<PERSON><PERSON>", "margelo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "McAfee", "mchmod", "MCTEST", "mechler", "mediumitalic", "memberof", "metainfo", "metatags", "microtime", "Microtransaction", "microtransactions", "midoffice", "mimecast", "Miniwarehouses", "miterlimit", "mkcert", "MMYY", "<PERSON><PERSON><PERSON>", "mobiexpensifyg", "mobileprovision", "mswin", "msword", "mtrl", "multidex", "MVCP", "MYOB", "my<PERSON>b<PERSON><PERSON>", "<PERSON><PERSON>", "NAICS", "Namecheap", "Nanobiotechnology", "<PERSON><PERSON>", "navattic", "navigations", "nbta", "ndkversion", "Nederlands", "negsign", "netinfo", "netrc", "NETSUITE", "Neue", "newarch", "NEWDOT", "newdotreport", "NEWEXPENSIFY", "newhelp", "ngneat", "NGROK", "NLRA", "nmanager", "NMLS", "nocreeps", "nodownload", "Nonchocolate", "<PERSON><PERSON><PERSON><PERSON>", "Nondepository", "Nonfinancial", "Nonmortgage", "<PERSON><PERSON><PERSON>", "nonreimbursable", "Nonstore", "Nonupholstered", "noopener", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nosymbol", "Noto", "NSQS", "NSQSOAuth", "NSURL", "NSUTF8", "ntag", "ntdiary", "NTSB", "Nuevo", "nullptr", "numberformat", "objc", "oblador", "OCBC", "octocat", "officedocument", "oldarch", "OLDDOT", "onclosetag", "Oncorp", "oneteam", "oneui", "on<PERSON>do", "onloaderror", "onopentag", "onplayerror", "onxy", "ONYXKEY", "ONYXKEYS", "openxmlformats", "ordinality", "organisation", "originalamount", "originalcurrency", "osdk", "o<PERSON><PERSON><PERSON>", "outplant", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Parcelable", "passplus", "passwordless", "Passwordless", "payrollcode", "pbxproj", "pdfreport", "pdfs", "<PERSON><PERSON><PERSON>", "persistable", "<PERSON><PERSON><PERSON>", "phonenumber", "Picklist", "picklists", "PINGPONG", "pkill", "Pluginfile", "<PERSON><PERSON><PERSON>", "pnrs", "Podfile", "podspec", "podspecs", "Pokdu<PERSON>s", "POLICYCHANGELOG_ADD_EMPLOYEE", "<PERSON><PERSON>", "popen3", "Português", "<PERSON><PERSON><PERSON><PERSON>", "Postharvest", "Postproduction", "Powerform", "POWERFORM", "Précédent", "precheck", "Precheck", "prescribers", "presentationml", "Pressable", "Pressables", "prettierrc", "proguard", "Proofpoint", "Protip", "purchaseamount", "purchasecurrency", "QAPR", "qrcode", "QUICKBOOKS", "RAAS", "rach", "<PERSON><PERSON><PERSON>", "Rankable", "RCTI18nUtil", "RCTIs", "RCTURL", "reactnative", "reactnativebackgroundtask", "reactnativehybridapp", "reactnativekeycommand", "reauthentication", "Reauthenticator", "Rebooked", "REBOOKED", "rebooking", "recategorize", "recents", "REDIRECTURI", "regexpu", "reimagination", "<PERSON><PERSON><PERSON>", "reimbursability", "Reimbursability", "Reimbursables", "reimbursementid", "REIMBURSER", "reimbursible", "Reimbursments", "remotedesktop", "remotesync", "requestee", "Resawing", "resizeable", "resultsbox", "retryable", "Reupholstery", "rideshare", "rnef", "RNFS", "rnmapbox", "RNTL", "RNVP", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "rpartition", "rstrip", "RTER", "s3uqn2oe4m85tufi6mqflbfbuajrm2i3", "SAASPASS", "safesearch", "Salagatan", "samltool", "Saqbd", "SBFJ", "Scaleway", "Scaleway's", "schedulable", "Schengen", "<PERSON><PERSON><PERSON><PERSON>", "SCIM", "scriptname", "seamless", "Segoe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>a", "serveo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "shellcheck", "shellenv", "shipit", "shouldshowellipsis", "signingkey", "signup", "Signup", "simctl", "skip_codesigning", "<PERSON><PERSON><PERSON><PERSON>", "SMARTREPORT", "Smartscan", "soloader", "SONIFICATION", "Speedscope", "<PERSON><PERSON><PERSON>", "spreadsheetml", "SSAE", "startdate", "stdev", "stdlib", "storepass", "STORYLANE", "strikethrough", "Strikethrough", "subcomponents", "sublicensees", "sublocality", "subpremise", "subrates", "substep", "substeps", "subtab", "subtabs", "subview", "Subviews", "superapp", "Supercenters", "superpowered", "supportal", "SVFG", "SVGID", "svgs", "Svmy", "Swipeable", "symbolicate", "symbolicated", "Symbolicates", "symbolication", "systempreferences", "tabindex", "Talkspace", "TBUM", "teachersunite", "Tele", "Teleproduction", "testflight", "TIMATIC", "Timothée", "tnode", "tobe", "togglefullscreen", "TOTP", "touchables", "Touchless", "TQBQW", "Trainline", "tranid", "Transpiles", "trivago", "Typeform", "uatp", "UATP", "UBOI", "UBOS", "UDID", "UDIDS", "UIBG", "<PERSON><PERSON><PERSON>", "ukkonen", "unapprove", "unapproves", "unassigning", "Unassigning", "unassigns", "uncategorized", "Undelete", "unheld", "unhold", "Unlaminated", "Unmigrated", "unredacted", "unscrollable", "unsubmitted", "UNSWIPEABLE", "Unvalidated", "UPWORK", "urbanairship", "urlset", "USAA", "USDVBBA", "useAutolayout", "usernotifications", "utilise", "Valusk<PERSON>", "VBBA", "Ven<PERSON>", "viewability", "viewport", "viewports", "VMPD", "voidings", "vorbis", "vvcf", "vypj", "waitlist", "waitlisted", "<PERSON><PERSON><PERSON>", "WDYR", "webapps", "webcredentials", "webrtc", "welldone", "Woohoo", "Wordmark", "wordprocessingml", "worklet", "workletization", "worklets", "workshopping", "workspacename", "writeitdown", "xcconfig", "xcodeproj", "xcpretty", "xcprivacy", "xcrun", "xcshareddata", "xctrace", "xcuserdata", "xcworkspace", "xdescribe", "xero", "Xfermode", "xlarge", "xlink", "xmlgateway", "Xours", "<PERSON><PERSON><PERSON>", "Yapl", "YAPL", "<PERSON><PERSON>", "yourcompany", "yourname", "YYMM", "zencdn", "Zenefit", "Zenefits", "zipalign", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zoneinfo", "zxcv", "zxldvw", "inputmethod", "copyable", "مثال"], "ignorePaths": ["src/languages/de.ts", "src/languages/es.ts", "src/languages/fr.ts", "src/languages/it.ts", "src/languages/ja.ts", "src/languages/nl.ts", "src/languages/pl.ts", "src/languages/pt-BR.ts", "src/languages/zh-hans.ts", "android/app/BUCK", "android/app/build_defs.bzl", "android/app/build.gradle", "android/app/google-services-DEV.json", "android/app/google-services.json", "android/app/proguard-rules.pro", "android/app/src/development/assets/airshipconfig.properties", "android/build.gradle", "android/gradle.properties", "android/gradle/wrapper/gradle-wrapper.properties", "android/gradlew", "android/gradlew.bat", "assets/emojis/common.ts", "assets/emojis/en.ts", "assets/emojis/es.ts", "patches/**", "docs/assets/Files/**", "docs/sitemap.xml", "ios/NewExpensify.xcodeproj/**", "ios/GoogleService-Info.plist", "ios/GoogleService-Info-DEV.plist", "ios/AirshipConfig.plist", "Mobile-Expensify", "modules/ExpensifyNitroUtils/nitrogen/**", "modules/ExpensifyNitroUtils/android/build.gradle", "modules/ExpensifyNitroUtils/android/src/main/cpp/**", "src/TIMEZONES.ts", "tests/unit/EmojiTest.ts", "tests/unit/FastSearchTest.ts", "tests/unit/LocaleCompareTest.ts", "tests/unit/removeInvisibleCharacters.ts", "tests/unit/searchCountryOptionsTest.ts", "tests/unit/currencyList.json", "tests/unit/ValidationUtilsTest.ts", "src/CONST/index.ts", "src/libs/SearchParser/*"], "ignoreRegExpList": ["@assets/.*"], "useGitignore": true}