# Application Philosophies
The following pages include details on how different systems are meant to be used.

The key words "MUST", "MUST NOT", "REQUIRED", "SHALL", "SHALL NOT", "SHOULD", "SHOULD NOT", "RECOMMENDED", "MAY", and
"OPTIONAL" are to be interpreted as described in [RFC 2119](https://datatracker.ietf.org/doc/html/rfc2119).

## Contents
* [Cross-Platform Philosophy](/contributingGuides/philosophies/CROSS-PLATFORM.md)
* [Offline Philosophy](/contributingGuides/philosophies/OFFLINE.md)
* [Routing Philosophy](/contributingGuides/philosophies/ROUTING.md)
